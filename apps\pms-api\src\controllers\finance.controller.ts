import { BadRequestException, Controller, Get, Query, Req } from '@nestjs/common';
import { FeeSettingModel } from '../providers/models/finance/feeSetting.model';
import { FeeTypeModel } from '../providers/models/finance/feeType.model';
import { WalletModel } from '../providers/models/finance/wallet.model';
import { FinanceService } from '../providers/services/finance.service';
import { ParentModel } from '../providers/models/parent/parent.model';
// /import { KidDetailsResponse } from '../providers/models/finance/financ.model';
@Controller()
export class FinanceController {
  constructor(private readonly service: FinanceService) {}

  @Get('payment/generatePayments')
  public async generatePayments(
    parentId?: number,
    kidId?: number,
  ): Promise<WalletModel> {
    // -- not used
    const response = await this.service.generatePayments(parentId, kidId);
    return response;
  }

  @Get('payment/getParentPendingPayments')
  public async getParentPendingPayments(
    parentId?: number,
    kidId?: number,
  ): Promise<FeeSettingModel[]> {
    // -- not used
    const response = await this.service.getParentPendingPayments(
      parentId,
      kidId,
    );
    return response;
  }

  @Get('payment/getParentUpcomingPayments')
  public async getParentUpcomingPayments(
    parentId?: number,
    kidId?: number,
  ): Promise<FeeTypeModel[]> {
    // -- not used
    const response = await this.service.getParentUpcomingPayments(
      parentId,
      kidId,
    );
    return response;
  }

  @Get('payment/getParentPaymentHistory')
  public async getParentPaymentHistory(
   @Query('parentId') parentId?: number,
  @Query('kidId') kidId?: number,
  ): Promise<WalletModel[]> {
    // -- not used
    const response = await this.service.getParentPaymentHistory(
      parentId,
      kidId,
    );
    return response;
  }

  @Get('payment/getKidsPaymentsByParentId')
  public async getKidsPaymentsByParentId(
    @Query('parentId') parentId?: string,
  ): Promise<any> {
    // -- not used
    const response = await this.service.getKidsPaymentsByParentId(parentId);
    return response;
  }

  @Get('payment/getPaymentCertificateData')
  public async getPaymentCertificateData(
    kidInfoId?: number,
  ): Promise<WalletModel> {
    // -- not used
    const response = await this.service.getPaymentCertificateData(kidInfoId);
    return response;
  }

  @Get('payment/getKidsDetailsByParentId')
  public async getKidsDetailsByParentId(
    @Query('parentId') parentId?: string,
    @Req() req?: any,
  ): Promise<any[]> {
    // -- not used
    try {
        const pmsProfile = req?.user?.pmsProfile;
        if (pmsProfile) {
          const parsedProfile = typeof pmsProfile === 'string' ? JSON.parse(pmsProfile) : pmsProfile;
          parentId = (parsedProfile?.PersonId);
        }
      } catch (e) {
        console.error('Failed to parse pmsProfile:', e);
      }
    
      if (!parentId) {
        throw new BadRequestException('Parent ID could not be resolved from user profile.');
      }
    const response = await this.service.getKidsDetailsByParentId(parentId);
    return response;
  }

  @Get('student/getStudentPaymentReport')
  public async getStudentPaymentReport(
    userId?: number,
    selAcademicYearId?: number,
  ): Promise<WalletModel[]> {
    // -- not used
    const response = await this.service.getStudentPaymentReport(
      userId,
      selAcademicYearId,
    );
    return response;
  }
}
