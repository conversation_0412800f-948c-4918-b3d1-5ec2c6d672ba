import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FamilyModel } from '../providers/models/family/family.model';
import { MaritalStatusModel } from '../providers/models/family/maritalStatus.model';
import { FamilyService } from '../providers/services/family.service';
@Controller()
export class FamilyController {
  constructor(private readonly service: FamilyService) {}

  @Post('family/addUpdateFamily')
  public async addUpdateFamily(
    @Body() body: FamilyModel,@Req() req?: any,
  ): Promise<FamilyModel> {

    let personId: string | undefined ;

  if (!personId && req?.user?.pmsProfile) {
    try {
      const parsedProfile = JSON.parse(req.user.pmsProfile);
      personId = parsedProfile?.PersonId;
    } catch (e) {
      console.error('Failed to parse pmsProfile:', e);
    }
  }

  if (!personId) {
    throw new BadRequestException('Parent ID could not be resolved');
  }  body.parentId = Number(personId);


    // -- not used
    const response = await this.service.addUpdateFamily(body);
    return response;
  }

  @Post('family/addUpdateSpouse')
  public async addUpdateSpouse(
    @Body() body: MaritalStatusModel,
  ): Promise<MaritalStatusModel> {
    // -- not used
    const response = await this.service.addUpdateSpouse(body);
    return response;
  }

  @Post('upload/UploadFileToS3')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFileToS3(@UploadedFile() file: Express.Multer.File) {
    return this.service.uploadFileToS3(file);
  }

  // @Post('upload/GeneratePresignedUrlForDownload')
  // public async GeneratePresignedUrlForDownload(
  //       @Query('IsCertificate') IsCertificate: boolean,
  //       @Query('IsThumbNail') IsThumbNail: boolean,
  //   @Body() body: any,

  // ): Promise<any> {
  //   // -- not used
  //   const response = await this.service.GeneratePresignedUrlForDownload(
  //     IsCertificate,
  //     IsThumbNail,body
  //   );
  //   return response;
  // }


//   @Post('upload/GeneratePresignedUrlForDownload')
// public async GeneratePresignedUrlForDownload(
//   @Query('IsCertificate') IsCertificate: boolean,
//   @Query('IsThumbNail') IsThumbNail: boolean,
//   // @Body() fileKey: string, // receives the raw string like "file-name.png"
// ): Promise<any> {
//   const response = await this.service.GeneratePresignedUrlForDownload(
//     IsCertificate,
//     IsThumbNail,
//     // fileKey
//   );
//   return response.data; // return only the data part
// }


  @Get('family/getFamilyDetailsByParentId')
  public async getFamilyDetailsByParentId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    parentId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.service.getFamilyDetailsByParentId(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      parentId,
    );
    return response;
  }

  @Get('family/removeFamilySpouseKid')
  public async removeFamilySpouseKid(
    familyId?: number,
    spouseId?: number,
    kidId?: number,
  ): Promise<FamilyModel> {
    // -- not used
    const response = await this.service.removeFamilySpouseKid(
      familyId,
      spouseId,
      kidId,
    );
    return response;
  }

  @Get('family/getFamilyDetailsByParentSpouseId')
  public async getFamilyDetailsByParentSpouseId(
    @Query('parentId') parentId: string, @Req() req?: any,
  ): Promise<FamilyModel[]> {
  let personId: string | undefined ;

  if (!personId && req?.user?.pmsProfile) {
    try {
      const parsedProfile = JSON.parse(req.user.pmsProfile);
      personId = parsedProfile?.PersonId;
    } catch (e) {
      console.error('Failed to parse pmsProfile:', e);
    }
  }

  if (!personId) {
    throw new BadRequestException('Parent ID could not be resolved');
  }

    // -- not used
  //    let parentId: string | undefined;
  //     try {
  //   const pmsProfile = req?.user?.pmsProfile;
  //   if (pmsProfile) {
  //     const parsedProfile = JSON.parse(pmsProfile);
  //     parentId = parsedProfile?.PersonId;
  //   }
  // } catch (error) {
  //   console.error('Failed to parse pmsProfile:', error);
  // }
    const response =
      await this.service.getFamilyDetailsByParentSpouseId(personId);
    return response;
  }

  @Get('family/getFamilyDetailsByFamilyId')
  public async getFamilyDetailsByFamilyId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    familyId?: number,
    parentId?: number,
    @Req() req?: any,
  ): Promise<FamilyModel[]> {
    try {
    const pmsProfile = req?.user?.pmsProfile;
    if (pmsProfile) {
      const parsedProfile = typeof pmsProfile === 'string' ? JSON.parse(pmsProfile) : pmsProfile;
      parentId = Number(parsedProfile?.PersonId);
    }
  } catch (e) {
    console.error('Failed to parse pmsProfile:', e);
  }

  if (!parentId) {
    throw new BadRequestException('Parent ID could not be resolved from user profile.');
  }
    // -- not used
    const response = await this.service.getFamilyDetailsByFamilyId(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      familyId,
      parentId,
    );
    return response;
  }

  @Get('family/getFamilySummaryByFamilyId')
  public async getFamilySummaryByFamilyId(
    familyId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.service.getFamilySummaryByFamilyId(familyId);
    return response;
  }
}
