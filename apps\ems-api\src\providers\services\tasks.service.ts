import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { TasksModel } from '../models/tasks/tasks.model';
import { TasksChangedAttributesItemResponseModel } from '../models/tasks/tasksChangedAttributesItemResponse.model';
import { TasksItemResponseModel } from '../models/tasks/tasksItemResponse.model';
@Injectable()
export class TasksService {
  constructor(private readonly http: HttpService) {}

  public async getChangedAttributesById(
    identificationKey: string,
    changedAttributesUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<TasksChangedAttributesItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<TasksChangedAttributesItemResponseModel>(
        `tasks/${identificationKey}/child/changedAttributes/${changedAttributesUniqID}`,
        {
          IdentificationKey: identificationKey,
          changedAttributesUniqID: changedAttributesUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getTasksById(
    identificationKey?: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<TasksItemResponseModel[]> {
    // -- not used
    const response = await this.http.get<TasksItemResponseModel[]>(
      `tasks/${identificationKey}`,
      {
        // IdentificationKey: identificationKey,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      true,
    );
    return response;
  }

  public async getTasks(
    expand?: string,
    identificationKey?: string,
    assignment?: string,
    fields?: string,
    onlyData?: boolean,
    links?: string,
    limit?: number,
    offset?: number,
    totalResults?: boolean,
    q?: string,
    status?: string,
    orderBy?: string,
    finder?: string,
  ): Promise<TasksModel> {
    // -- not used
    const response = await this.http.get<TasksModel>(
      `tasks`,
      {
        expand: expand,
        identificationKey: identificationKey,
        assignment: assignment,
        fields: fields,
        onlyData: onlyData,
        links: links,
        limit: limit,
        offset: offset,
        totalResults: totalResults,
        q: q,
        status: status,
        orderBy: orderBy,
        finder: finder,
      },
      true,
    );
    return response;
  }

  // public async getTask(id: string): Promise<TasksModel[]> {
  //   return await this.http.get<TasksModel[]>(`tasks/${id}/history`);
  // }

  public async getTask(id: string): Promise<TasksModel[]> {
    // -- not used
    const response = await this.http.get<TasksModel[]>(`tasks/${id}/history`);
    return response;
  }

  public async putTasks(
    taskIds: string[],
    actionId: string,
    commentStr: string,
    _username: string,
  ): Promise<any> {
    // Construct request body according to Oracle BPM API specification
    const body = {
      tasks: taskIds,
      action: { id: actionId },
      comment: {
        commentStr: commentStr,
      },
    };

    return this.http.put('tasks', body, {});
  }
}
